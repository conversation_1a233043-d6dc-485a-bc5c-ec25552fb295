<template>
  <div class="content">
    <div class="search">
      <CommonHeader />
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <!-- <el-form-item>
          <el-input
            clearable
            v-model="searchForm.keywords"
            placeholder="输入关键字查询"
            @change="getList(1)"
          ></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-select
            v-model="searchForm.riverId"
            filterable
            clearable
            placeholder="选择河流查询"
            @change="getList(1)"
          >
            <el-option
              v-for="item in riverList"
              :key="item.riverId"
              :label="item.riverName"
              :value="item.riverId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="searchForm.date"
            @change="getList(1)"
            type="monthrange"
            value-format="yyyy-MM"
            style="width: 300px"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item style="float: right">
          <el-button
            type="primary"
            @click="dialogVisible = true"
            icon="el-icon-plus"
            >新增取样</el-button
          >
          <el-button @click="reset">重置</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <el-table
        ref="logTable"
        v-loading="loading"
        :data="tableData"
        class="tableCustomStyle"
        border
        :header-cell-style="{ background: '#F5F7FA' }"
      >
        <el-table-column
          label="序号"
          width="100px"
          type="index"
          align="center"
        />
        <el-table-column label="河流名称" align="center" prop="riverName" />
        <el-table-column label="取样时间" align="center">
          <template slot-scope="scope">{{
            scope.row.year + "-" + scope.row.month
          }}</template>
        </el-table-column>
        <el-table-column label="水质类别" align="center" prop="waterType" />
        <el-table-column label="氨氮(mg/L)" align="center" prop="nh3n" />
        <el-table-column
          label="COD(mg/L)"
          align="center"
          prop="dissolvedOxygen"
        />
        <el-table-column label="CODMn(mg/L)" align="center" prop="codmn" />
        <el-table-column
          label="总磷(mg/L)"
          align="center"
          prop="totalPhosphorus"
        />

        <el-table-column align="center" width="220px">
          <template slot="header" slot-scope="scope"
            >操作
          </template>
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-popover
              v-model="scope.row.visible"
              placement="top"
              width="200"
              title="删除确认"
            >
              <p style="color:#f56c6c;text-align: center;">
                确定要删除该记录吗？
              </p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" @click="scope.row.visible = false"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="remove(scope.row.waterAssessmentGoalId)"
                  >确定</el-button
                >
              </div>
              <span slot="reference" class="table-edit-tag color-red"
                >删除</span
              >
            </el-popover>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style="margin-top: 15px; justify-content: flex-end; display: flex"
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增、编辑弹框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="560px"
      destroy-on-close
      :close-on-click-modal="false"
      :title="isEdit ? '编辑' : '新增'"
      @closed="closeDialog"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="取样时间" prop="samplingTime">
          <el-date-picker
            style="width: 350px"
            v-model="form.samplingTime"
            type="month"
            value-format="yyyy-MM-01 00:00:00"
            placeholder="选择取样时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属河流" prop="riverId">
          <el-select
            v-model="form.riverId"
            style="width: 350px"
            filterable
            @change="riverChange"
            placeholder="请选择所属河流"
          >
            <el-option
              v-for="item in riverList"
              :key="item.riverId"
              :label="item.riverName"
              :value="item.riverId"
            ></el-option>
          </el-select>
        </el-form-item>
        <h4>监测因子：</h4>
        <el-form-item label="氨氮" prop="nh3n">
          <el-input-number
            v-model="form.nh3n"
            placeholder="请输入氨氮值"
            style="width: 350px;"
            :controls="false"
            :precision="2"
          />
          <span class="unit">mg/L</span>
        </el-form-item>
        <el-form-item label="COD" prop="dissolvedOxygen">
          <el-input-number
            v-model="form.dissolvedOxygen"
            placeholder="请输入COD值"
            style="width: 350px"
            :controls="false"
            :precision="2"
          />
          <span class="unit">mg/L</span>
        </el-form-item>
        <el-form-item label="高猛酸盐指数" prop="codmn">
          <el-input-number
            v-model="form.codmn"
            placeholder="请输入高猛酸盐指数值"
            style="width: 350px"
            :controls="false"
            :precision="2"
          />
          <span class="unit">mg/L</span>
        </el-form-item>
        <el-form-item label="总磷" prop="totalPhosphorus">
          <el-input-number
            v-model="form.totalPhosphorus"
            placeholder="请输入总磷值"
            style="width: 350px"
            :controls="false"
            :precision="2"
          />
          <span class="unit">mg/L</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          v-loading="submitLoading"
          @click="handleSubmit"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 重复年度提示 -->
    <el-dialog
      :visible.sync="repeatYearVisible"
      width="300px"
      title="提示"
      destroy-on-close
    >
      <p>
        已存在<span style="color: red;">{{ repeatYear }}</span
        >数据，是否进行覆盖替换？
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="repeatYearVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRepeatYear">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  riverList,
  warterEnvDataPageList,
  goalMonthSave,
  goalMonthUpdate,
  goalMonthDel,
  goalMonthExport
} from "@/api/health";
export default {
  data() {
    return {
      searchForm: {
        keywords: "",
        riverId: "",
        date: null
      },
      riverList: [],

      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      riverId: "",
      // 新增编辑表单相关：
      dialogVisible: false,
      isEdit: false,
      form: {
        waterAssessmentGoalId: "",
        samplingTime: "",
        riverId: "",
        riverName: "",
        nh3n: "",
        dissolvedOxygen: "",
        codmn: "",
        totalPhosphorus: ""
      },
      rules: {
        samplingTime: [{ required: true, message: "请选择取样时间" }],
        riverId: [{ required: true, message: "请选择所属河流" }],
        nh3n: [
          { required: true, message: "请输入氨氮值" },
          { pattern: /^[0-9]+(\.[0-9]+)?$/, message: "请输入数字" }
        ],
        dissolvedOxygen: [
          { required: true, message: "请输入COD值" },
          { pattern: /^[0-9]+(\.[0-9]+)?$/, message: "请输入数字" }
        ],
        codmn: [
          { required: true, message: "请输入高猛酸盐指数值" },
          { pattern: /^[0-9]+(\.[0-9]+)?$/, message: "请输入数字" }
        ],
        totalPhosphorus: [
          { required: true, message: "请输入总磷值" },
          { pattern: /^[0-9]+(\.[0-9]+)?$/, message: "请输入数字" }
        ]
      },
      submitLoading: false,
      repeatYear: "",
      repeatYearVisible: false,
      exportLoading: false
    };
  },
  created() {
    this.getRiverList();
    this.getList();
  },
  methods: {
    async getRiverList() {
      const res = await riverList();
      this.riverList = res.data.data;
    },
    async getList(pageNum) {
      this.loading = true;
      try {
        const res = await warterEnvDataPageList({
          riverId: this.searchForm.riverId,
          startDate: this.searchForm.date
            ? this.searchForm.date[0] + "-01"
            : undefined,
          endDate: this.searchForm.date
            ? `${this.searchForm.date[1]}-${new Date(
                this.searchForm.date[1].split("-")[0],
                this.searchForm.date[1].split("-")[1],
                0
              ).getDate()}`
            : undefined,
          pageNum: pageNum || this.pageNum,
          pageSize: this.pageSize
        });
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
        this.pageNum = res.data.data.current;
      } finally {
        this.loading = false;
      }
    },
    handleCurrentChange(page) {
      this.getList(page);
    },
    reset() {
      this.searchForm = {
        keywords: "",
        riverId: "",
        date: null
      };
      this.getList(1);
    },
    riverChange(id) {
      console.log(id);
      this.form.riverName = this.riverList.find(
        item => item.riverId === id
      ).riverName;
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const river = await warterEnvDataPageList({
            riverId: this.form.riverId,
            startDate: this.form.samplingTime.split(" ")[0],
            endDate: `${new Date(
              this.form.samplingTime
            ).getFullYear()}-${(new Date(this.form.samplingTime).getMonth() +
              1).toString().padStart(2, '0')}-${new Date(
              new Date(this.form.samplingTime).getFullYear(),
              new Date(this.form.samplingTime).getMonth() + 1,
              0
            ).getDate()}`,
            pageNum: 1,
            pageSize: 100
          });
          if (river.data.data.records.length > 0 && !this.isEdit) {
            this.repeatYearVisible = true;
            this.repeatYear = `${new Date(
              this.form.samplingTime
            ).getFullYear()}-${new Date(this.form.samplingTime).getMonth() +
              1}`;
            return;
          }
          this.submitLoading = true;
          const api = this.isEdit ? goalMonthUpdate : goalMonthSave;
          const item = river.data.data.records.find(item => item.riverId == this.form.riverId) || {}
          api({
            ...this.form,
            waterAssessmentGoalId: this.form.waterAssessmentGoalId || item.waterAssessmentGoalId
          })
            .then(res => {
              this.$message.success("操作成功");
              this.submitLoading = false;
              this.dialogVisible = false;
              this.getList();
            })
            .catch(() => {
              this.submitLoading = false;
            });
        }
      });
    },
    handleRepeatYear() {
      this.repeatYearVisible = false;
      this.isEdit = true;
      this.handleSubmit();
    },
    closeDialog() {
      this.isEdit = false;
      this.form = {
        waterAssessmentGoalId: "",
        samplingTime: "",
        riverId: "",
        riverName: "",
        nh3n: "",
        dissolvedOxygen: "",
        codmn: "",
        totalPhosphorus: ""
      };
      this.$refs.form.resetFields();
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.isEdit = true;
      const {
        waterAssessmentGoalId,
        riverId,
        riverName,
        totalPhosphorus,
        nh3n,
        dissolvedOxygen,
        codmn,
        samplingTime
      } = row;
      this.form = {
        waterAssessmentGoalId,
        riverId,
        riverName,
        totalPhosphorus,
        nh3n,
        dissolvedOxygen,
        codmn,
        samplingTime
      };
    },
    remove(id) {
      goalMonthDel(id)
        .then(res => {
          this.$message.success("删除成功");
          if (this.tableData.length === 1) this.pageNum--;
          this.getList();
        })
        .catch(() => {});
    },
    handleExport() {
      this.exportLoading = true;
      goalMonthExport({
        riverId: this.searchForm.riverId,
        startDate: this.searchForm.date
          ? this.searchForm.date[0] + "-01"
          : undefined,
        endDate: this.searchForm.date
          ? `${this.searchForm.date[1]}-${new Date(
              this.searchForm.date[1].split("-")[0],
              this.searchForm.date[1].split("-")[1],
              0
            ).getDate()}`
          : undefined
      })
        .then(res => {
          this.exportLoading = false;
          // 下载二进制流
          const blob = new Blob([res.data], {
            type: "application/vnd.ms-excel"
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "水质监测数据.xlsx";
          a.click();
          a.remove();
        })
        .catch(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 15px;
  .search {
    background: #fff;
    padding: 15px;
  }
}
.table {
  padding: 15px;
  background: #fff;
  margin-top: 15px;
}

::v-deep .el-input-number {
  .el-input__inner {
    text-align: left;
  }
}

.unit {
  position: absolute;
  right: 55px;
  top: 0;
  color: #c4c7cf;
  pointer-events: none
}
</style>
