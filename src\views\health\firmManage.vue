<template>
  <div class="content">
    <div class="search">
      <CommonHeader />
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item>
          <el-input
            clearable
            v-model="searchForm.keywords"
            placeholder="输入企业名称、统一社会信用代码查询"
            @change="getList(1)"
            style="width: 300px;"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="searchForm.industryCodes"
            clearable
            placeholder="行业类别"
            @change="getList(1)"
            style="width: 150px;"
          >
            <el-option label="制造业" value="制造业"></el-option>
            <el-option label="建筑业" value="建筑业"></el-option>
            <el-option label="服务业" value="服务业"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="float: right;">
          <el-button @click="reset">重置</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <el-table
        ref="firmTable"
        v-loading="loading"
        :data="tableData"
        class="tableCustomStyle"
        border
        :header-cell-style="{ background: '#F5F7FA' }"
      >
        <el-table-column
          label="序号"
          width="80px"
          type="index"
          align="center"
        />
        <el-table-column
          label="统一社会信用代码"
          align="center"
          prop="creditCode"
          width="180px"
        />
        <el-table-column
          label="企业名称"
          align="center"
          prop="enterName"
          width="200px"
        />
        <el-table-column
          label="行业类别"
          align="center"
          prop="industryName"
          width="120px"
        />
        <el-table-column
          label="所属区域"
          align="center"
          prop="districtName"
          width="100px"
        />
        <el-table-column
          label="所属街道"
          align="center"
          prop="streetName"
          width="100px"
        />
        <el-table-column
          label="生产经营场所地址"
          align="center"
          prop="opeAddress"
          width="250px"
        />
        <el-table-column
          label="技术负责人"
          align="center"
          prop="concatPerson"
          width="120px"
        />
        <el-table-column
          label="联系人电话"
          align="center"
          prop="mobilePhone"
          width="130px"
        />
        <el-table-column
          label="操作"
          align="center"
          width="100px"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetail(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style=" display: flex; justify-content: flex-end; margin-top: 15px;"
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { firmList, firmExport } from "@/api/firmManage";
import CommonHeader from "@/components/commonHeader";

export default {
  name: "FirmManage",
  components: {
    CommonHeader
  },
  data() {
    return {
      searchForm: {
        keywords: "",
        permitManageType: "",
        streetCode: "",
        industryCodes: ""
      },
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      exportLoading: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList(pageNum) {
      this.loading = true;
      try {
        const res = await firmList({
          keywords: this.searchForm.keywords,
          permitManageType: this.searchForm.permitManageType,
          streetCode: this.searchForm.streetCode,
          industryCodes: this.searchForm.industryCodes
        });
        if (res.data.code === 0) {
          // 大数据接口返回的是数组，不是分页对象
          this.tableData = res.data.data || [];
          this.total = this.tableData.length;
          this.pageNum = pageNum || 1;
        }
      } catch (error) {
        console.error("获取企业列表失败:", error);
        this.$message.error("获取企业列表失败");
      } finally {
        this.loading = false;
      }
    },
    handleCurrentChange(page) {
      this.getList(page);
    },
    reset() {
      this.searchForm = {
        keywords: "",
        permitManageType: "",
        streetCode: "",
        industryCodes: ""
      };
      this.getList(1);
    },
    handleDetail() {
      // 详情功能暂时不实现，根据需求后续添加
      this.$message.info("详情功能开发中...");
    },
    async handleExport() {
      this.exportLoading = true;
      try {
        const res = await firmExport({
          keywords: this.searchForm.keywords,
          permitManageType: this.searchForm.permitManageType,
          streetCode: this.searchForm.streetCode,
          industryCodes: this.searchForm.industryCodes
        });
        // 下载二进制流
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel"
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "企业信息.xlsx";
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败");
      } finally {
        this.exportLoading = false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 15px;
  .search {
    padding: 15px;
    background: #ffffff;
  }
}
.table {
  padding: 15px;
  margin-top: 15px;
  background: #ffffff;
}
</style>
