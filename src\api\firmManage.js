import request from "@/utils/request";

// 分页查询企业
export function firmPageList(params) {
  return request({
    url: "/water/sewage/enter/pageList",
    method: "get",
    params
  });
}

// 企业列表（大数据接口，返回字段更完整）
export function firmList(params) {
  return request({
    url: "/water/bigData/sewage/enter/list",
    method: "get",
    params
  });
}

// 获取企业详情
export function firmDetail(params) {
  return request({
    url: "/water/sewage/enter/getDetail",
    method: "get",
    params
  });
}

// 导出企业信息
export function firmExport(params) {
  return request({
    url: "/water/sewage/enter/export",
    method: "get",
    params,
    responseType: "blob"
  });
}

// 新增企业
export function firmAdd(data) {
  return request({
    url: "/water/sewage/enter/save",
    method: "post",
    data
  });
}

// 更新企业
export function firmUpdate(data) {
  return request({
    url: "/water/sewage/enter/update",
    method: "put",
    data
  });
}

// 删除企业
export function firmDelete(id) {
  return request({
    url: `/water/sewage/enter/delete?id=${id}`,
    method: "delete"
  });
}
